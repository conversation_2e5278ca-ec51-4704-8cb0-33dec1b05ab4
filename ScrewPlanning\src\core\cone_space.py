"""
锥形空间生成模块

基于论文算法生成螺钉路径搜索的锥形空间
"""

import numpy as np
import math
from typing import List, Tuple, Optional
import logging

try:
    from ..utils.geometry import Point3D, Vector3D, RotationMatrix, ScrewPath, ConeSpace
except ImportError:
    from utils.geometry import Point3D, Vector3D, RotationMatrix, ScrewPath, ConeSpace


class ConeSpaceGenerator:
    """锥形空间生成器"""

    def __init__(self, constraint_angle: float = 45.0,
                 screw_length: float = 38.97,
                 screw_radius: float = 1.65):
        """
        初始化锥形空间生成器

        Args:
            constraint_angle: 约束角度（度）
            screw_length: 螺钉长度 (mm)
            screw_radius: 螺钉半径 (mm)
        """
        self.constraint_angle = math.radians(constraint_angle)
        self.screw_length = screw_length
        self.screw_radius = screw_radius

        logging.info(f"锥形空间生成器初始化完成")
        logging.info(f"约束角度: {constraint_angle}°")
        logging.info(f"螺钉长度: {screw_length}mm")
        logging.info(f"螺钉半径: {screw_radius}mm")
    
    def generate_cone_space(self, apex: Point3D, axis_direction: Vector3D,
                          screw_type: Optional[str] = None) -> ConeSpace:
        """
        生成锥形空间

        Args:
            apex: 锥顶点（螺钉起始点）
            axis_direction: 锥轴方向
            screw_type: 螺钉类型，用于确定是否使用半锥约束

        Returns:
            锥形空间对象
        """
        # 根据螺钉类型确定有效约束角度
        if screw_type in ["top", "bottom"]:
            # 半锥约束：使用一半的约束角度
            effective_angle = self.constraint_angle / 2
        else:
            # 完整锥形：使用完整约束角度
            effective_angle = self.constraint_angle

        # 计算锥底半径
        base_radius = self.screw_length * math.tan(effective_angle)

        cone_space = ConeSpace(
            apex=apex,
            axis=axis_direction,
            height=self.screw_length,
            base_radius=base_radius,
            constraint_angle=math.degrees(effective_angle)
        )

        return cone_space
    
    def generate_candidate_paths(self, cone_space: ConeSpace,
                               radial_resolution: int = 30,
                               circumferential_resolution: int = 150,
                               screw_type: Optional[str] = None,
                               base_center: Optional[Point3D] = None) -> List[ScrewPath]:
        """
        在锥形空间内生成候选路径

        Args:
            cone_space: 锥形空间
            radial_resolution: 径向分辨率
            circumferential_resolution: 周向分辨率
            screw_type: 螺钉类型 ("top", "bottom", "center" 或 None)
                       - "top": 生成上半锥路径（约束角度减半，只允许向上偏移）
                       - "bottom": 生成下半锥路径（约束角度减半，只允许向下偏移）
                       - "center" 或 None: 生成完整锥形路径
            base_center: 基座中心点，用于确定上下螺钉的相对位置

        Returns:
            候选路径列表
        """
        candidate_paths = []

        # 获取锥底中心
        base_center = cone_space.get_base_center()

        # 创建垂直于锥轴的两个正交向量
        axis = cone_space.axis
        if abs(axis.x) < 0.9:
            temp_vector = Vector3D(1, 0, 0)
        else:
            temp_vector = Vector3D(0, 1, 0)

        perpendicular1 = axis.cross(temp_vector).normalize()
        perpendicular2 = axis.cross(perpendicular1).normalize()

        # 半锥约束：对于上下螺钉，在对应的半空间内生成路径
        up_down_vector = None

        if screw_type in ["top", "bottom"] and base_center is not None:
            # 计算"上下"方向：从基座中心到螺钉起始点的向量
            screw_position_vector = (cone_space.apex - base_center).normalize()

            # 计算垂直于基座法向量的"上下"方向
            # 这个向量表示螺钉相对于基座中心的位置方向
            axis_direction = cone_space.axis
            # 将螺钉位置向量投影到垂直于基座法向量的平面上
            projection_length = screw_position_vector.dot(axis_direction)
            projection_array = projection_length * axis_direction.to_array()
            up_down_vector_array = screw_position_vector.to_array() - projection_array

            if np.linalg.norm(up_down_vector_array) > 1e-6:
                up_down_vector = Vector3D.from_array(up_down_vector_array).normalize()
                logging.info(f"{screw_type}螺钉的上下方向向量: {up_down_vector}")
                logging.info(f"使用约束角度: {math.degrees(cone_space.constraint_angle):.1f}°")
            else:
                logging.warning(f"{screw_type}螺钉位置与基座法向量平行，无法确定上下方向")
                screw_type = None  # 退回到完整锥形
        
        # 在锥底面生成均匀分布的点
        for r_idx in range(radial_resolution + 1):
            # 径向距离（从0到base_radius）
            if radial_resolution == 0:
                radius = 0
            else:
                radius = cone_space.base_radius * r_idx / radial_resolution

            # 对于中心点，只生成一个路径
            if radius == 0:
                end_point = base_center
                path = ScrewPath(
                    start_point=cone_space.apex,
                    end_point=end_point,
                    radius=self.screw_radius,
                    length=self.screw_length
                )
                candidate_paths.append(path)
                continue

            # 周向角度分布
            for theta_idx in range(circumferential_resolution):
                theta = 2 * math.pi * theta_idx / circumferential_resolution

                # 计算锥底面上的点
                offset_vector = (perpendicular1 * math.cos(theta) +
                               perpendicular2 * math.sin(theta)) * radius

                end_point_array = base_center.to_array() + offset_vector.to_array()
                end_point = Point3D.from_array(end_point_array)

                # 半锥约束检查
                if screw_type in ["top", "bottom"] and up_down_vector is not None:
                    # 计算路径方向向量
                    path_direction = (end_point - cone_space.apex).normalize()

                    # 检查路径是否在有效的半锥范围内
                    # 1. 首先检查路径是否在约束角度内
                    axis_direction = cone_space.axis
                    angle_with_axis = math.acos(max(-1, min(1, path_direction.dot(axis_direction))))

                    if angle_with_axis > math.radians(cone_space.constraint_angle):
                        continue  # 超出约束角度，跳过

                    # 2. 检查路径是否在正确的半空间内
                    # 计算路径在垂直于基座法向量平面上的分量
                    projection_length = path_direction.dot(axis_direction)
                    projection_array = projection_length * axis_direction.to_array()
                    perpendicular_array = path_direction.to_array() - projection_array

                    if np.linalg.norm(perpendicular_array) > 1e-6:
                        perpendicular_direction = Vector3D.from_array(perpendicular_array).normalize()
                        dot_product = perpendicular_direction.dot(up_down_vector)

                        # 对于上螺钉：只允许与上下方向向量同向的路径（dot_product > 0）
                        # 对于下螺钉：只允许与上下方向向量反向的路径（dot_product < 0）
                        if screw_type == "top" and dot_product < 0:
                            continue  # 跳过反向路径
                        elif screw_type == "bottom" and dot_product > 0:
                            continue  # 跳过同向路径

                # 创建候选路径
                path = ScrewPath(
                    start_point=cone_space.apex,
                    end_point=end_point,
                    radius=self.screw_radius,
                    length=self.screw_length
                )

                candidate_paths.append(path)
        
        if screw_type in ["top", "bottom"]:
            logging.info(f"生成{screw_type}螺钉半锥候选路径数量: {len(candidate_paths)}")
            logging.info(f"半锥约束: 约束角度={cone_space.constraint_angle:.1f}°，方向限制={screw_type}")
        else:
            logging.info(f"生成完整锥形候选路径数量: {len(candidate_paths)}")
            logging.info(f"完整锥形约束角度: {cone_space.constraint_angle:.1f}°")

        return candidate_paths
    
    def filter_paths_by_angle_constraint(self, paths1: List[ScrewPath], 
                                       paths2: List[ScrewPath],
                                       max_angle: float = 45.0) -> Tuple[List[ScrewPath], List[ScrewPath]]:
        """
        根据角度约束过滤路径组合
        
        Args:
            paths1: 螺钉1的候选路径
            paths2: 螺钉2的候选路径
            max_angle: 最大允许角度（度）
            
        Returns:
            过滤后的路径组合
        """
        max_angle_rad = math.radians(max_angle)
        filtered_paths1 = []
        filtered_paths2 = []
        
        for path1 in paths1:
            for path2 in paths2:
                # 计算两条路径的夹角
                dir1 = path1.get_direction_vector()
                dir2 = path2.get_direction_vector()
                
                dot_product = dir1.dot(dir2)
                # 限制dot_product在[-1, 1]范围内，避免数值误差
                dot_product = max(-1.0, min(1.0, dot_product))
                angle = math.acos(abs(dot_product))
                
                if angle <= max_angle_rad:
                    if path1 not in filtered_paths1:
                        filtered_paths1.append(path1)
                    if path2 not in filtered_paths2:
                        filtered_paths2.append(path2)
        
        logging.info(f"角度约束过滤后 - 螺钉1路径: {len(filtered_paths1)}, 螺钉2路径: {len(filtered_paths2)}")
        return filtered_paths1, filtered_paths2
    
    def calculate_prosthesis_parameters(self, reference_points: List[Point3D]) -> Tuple[Point3D, Vector3D, Vector3D]:
        """
        根据参考点计算假体参数
        
        Args:
            reference_points: 参考点列表 [p1, p2, p3, p4, p5, p6]
            
        Returns:
            (基座中心点, 法向量epsilon, 方向向量m)
        """
        if len(reference_points) < 4:
            raise ValueError("至少需要4个参考点")
        
        p1, p2, p3, p4 = reference_points[:4]
        
        # 计算假体平面的法向量
        v1 = p2 - p1
        v2 = p3 - p1
        epsilon = v1.cross(v2).normalize()
        
        # 计算方向向量m
        m = (p4 - p1).normalize()
        
        return p1, epsilon, m
