"""
测试半锥约束功能

验证上下螺钉的半锥约束是否正确实现
"""

import sys
import os
import math
import logging

# 添加src目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
sys.path.insert(0, src_dir)

from src.core.cone_space import ConeSpaceGenerator
from src.utils.geometry import Point3D, Vector3D, ConeSpace


def test_half_cone_constraint():
    """测试半锥约束功能"""
    print("🧪 测试半锥约束功能")

    # 设置日志
    logging.basicConfig(level=logging.INFO)

    # 创建锥形空间生成器
    generator = ConeSpaceGenerator(constraint_angle=15.0, screw_length=20.0, screw_radius=3.25)

    # 使用更真实的测试数据（模拟实际的肩盂基座螺钉坐标）
    # 假设基座法向量指向X轴正方向（肩盂内部）
    base_center = Point3D(0, 0, 0)  # 基座中心
    top_screw_start = Point3D(-5, 8, 3)  # 上螺钉起始点（在基座外侧，Y轴正方向，Z轴正方向）
    bottom_screw_start = Point3D(-5, -8, -3)  # 下螺钉起始点（在基座外侧，Y轴负方向，Z轴负方向）
    axis_direction = Vector3D(1, 0, 0)  # 基座法向量（X轴正方向，指向肩盂内部）

    print(f"上螺钉到基座中心向量: {(base_center - top_screw_start).normalize()}")
    print(f"下螺钉到基座中心向量: {(base_center - bottom_screw_start).normalize()}")
    
    print(f"\n📊 测试参数:")
    print(f"基座中心: ({base_center.x}, {base_center.y}, {base_center.z})")
    print(f"上螺钉起始点: ({top_screw_start.x}, {top_screw_start.y}, {top_screw_start.z})")
    print(f"下螺钉起始点: ({bottom_screw_start.x}, {bottom_screw_start.y}, {bottom_screw_start.z})")
    print(f"基座法向量: ({axis_direction.x}, {axis_direction.y}, {axis_direction.z})")
    print(f"约束角度: 15.0°")

    # 测试不同螺钉类型的路径生成
    test_cases = [
        ("完整锥形", None, top_screw_start, None),
        ("上螺钉半锥", "top", top_screw_start, base_center),
        ("下螺钉半锥", "bottom", bottom_screw_start, base_center),
        ("中心螺钉", "center", base_center, base_center)
    ]
    
    results = {}

    for case_name, screw_type, screw_start, test_base_center in test_cases:
        print(f"\n🔍 测试 {case_name} (screw_type='{screw_type}')")

        # 为每个测试用例生成锥形空间
        cone_space = generator.generate_cone_space(screw_start, axis_direction)

        # 生成候选路径
        candidate_paths = generator.generate_candidate_paths(
            cone_space,
            radial_resolution=10,  # 减少分辨率以便观察
            circumferential_resolution=36,  # 每10度一个点
            screw_type=screw_type,
            base_center=test_base_center
        )
        
        # 分析路径方向（相对于基座中心）
        toward_base_paths = 0  # 朝向基座中心的路径
        away_from_base_paths = 0  # 远离基座中心的路径
        neutral_paths = 0  # 中性路径

        if test_base_center is not None:
            screw_to_base_vector = (screw_start - test_base_center).normalize()

            for path in candidate_paths:
                direction = path.get_direction_vector()
                dot_product = direction.dot(screw_to_base_vector)

                if dot_product > 0.01:
                    away_from_base_paths += 1  # 远离基座中心
                elif dot_product < -0.01:
                    toward_base_paths += 1  # 朝向基座中心
                else:
                    neutral_paths += 1  # 中性
        else:
            # 对于完整锥形，我们仍然分析Z方向
            for path in candidate_paths:
                direction = path.get_direction_vector()
                if direction.z > 0.01:
                    away_from_base_paths += 1
                elif direction.z < -0.01:
                    toward_base_paths += 1
                else:
                    neutral_paths += 1
        
        results[case_name] = {
            "total": len(candidate_paths),
            "toward_base": toward_base_paths,
            "away_from_base": away_from_base_paths,
            "neutral": neutral_paths
        }

        print(f"  总路径数: {len(candidate_paths)}")
        print(f"  朝向基座中心路径: {toward_base_paths}")
        print(f"  远离基座中心路径: {away_from_base_paths}")
        print(f"  中性路径: {neutral_paths}")
    
    # 验证约束效果
    print(f"\n✅ 约束验证结果:")
    print("-" * 60)
    
    # 验证上螺钉约束（应该只有远离基座中心的路径）
    top_result = results["上螺钉半锥"]
    if top_result["toward_base"] == 0:
        print("✅ 上螺钉约束正确：没有朝向基座中心的路径")
    else:
        print(f"❌ 上螺钉约束失败：仍有 {top_result['toward_base']} 条朝向基座中心的路径")

    # 验证下螺钉约束（应该只有朝向基座中心的路径）
    bottom_result = results["下螺钉半锥"]
    if bottom_result["away_from_base"] == 0:
        print("✅ 下螺钉约束正确：没有远离基座中心的路径")
    else:
        print(f"❌ 下螺钉约束失败：仍有 {bottom_result['away_from_base']} 条远离基座中心的路径")

    # 验证完整锥形
    full_result = results["完整锥形"]
    if full_result["toward_base"] > 0 and full_result["away_from_base"] > 0:
        print("✅ 完整锥形正确：包含两个方向的路径")
    else:
        print("❌ 完整锥形异常：缺少某个方向的路径")
    
    # 计算路径减少比例
    reduction_top = (full_result["total"] - top_result["total"]) / full_result["total"] * 100
    reduction_bottom = (full_result["total"] - bottom_result["total"]) / full_result["total"] * 100
    
    print(f"\n📈 路径数量对比:")
    print(f"完整锥形路径数: {full_result['total']}")
    print(f"上螺钉路径数: {top_result['total']} (减少 {reduction_top:.1f}%)")
    print(f"下螺钉路径数: {bottom_result['total']} (减少 {reduction_bottom:.1f}%)")
    
    return results


def test_angle_distribution():
    """测试角度分布"""
    print("\n🧪 测试角度分布")
    
    generator = ConeSpaceGenerator(constraint_angle=15.0, screw_length=20.0, screw_radius=3.25)
    apex = Point3D(0, 0, 0)
    axis_direction = Vector3D(0, 0, 1)
    cone_space = generator.generate_cone_space(apex, axis_direction)
    
    # 生成上螺钉路径
    top_paths = generator.generate_candidate_paths(
        cone_space, radial_resolution=5, circumferential_resolution=36, screw_type="top")
    
    # 分析角度分布
    angles = []
    for path in top_paths:
        direction = path.get_direction_vector()
        # 计算与Z轴的夹角
        angle = math.acos(max(-1, min(1, direction.z)))  # direction已经是单位向量
        angles.append(math.degrees(angle))
    
    if angles:
        print(f"上螺钉路径角度范围: {min(angles):.1f}° - {max(angles):.1f}°")
        print(f"平均角度: {sum(angles)/len(angles):.1f}°")
        print(f"期望范围: 0° - 15°")
        
        # 验证所有角度都在约束范围内
        valid_angles = all(angle <= 15.1 for angle in angles)  # 允许小的数值误差
        if valid_angles:
            print("✅ 所有路径角度都在约束范围内")
        else:
            print("❌ 存在超出约束范围的路径")
    else:
        print("❌ 没有生成任何路径")


if __name__ == "__main__":
    print("=" * 60)
    print("半锥约束功能测试")
    print("=" * 60)
    
    try:
        test_half_cone_constraint()
        test_angle_distribution()
        print("\n🎉 测试完成！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
